export interface User {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
}

export interface TaskImage {
  id: string;
  filename: string;
  url: string;
  mimeType: string;
  size: number;
  createdAt: string;
}

export interface TaskAttachment {
  id: string;
  filename: string;
  originalName: string;
  url: string;
  mimeType: string;
  size: number;
  description?: string;
  category?: string;
  createdAt: string;
  uploadedBy: User;
}

export interface Todo {
  id: string;
  title: string;
  isCompleted: boolean;
  timeSpent?: number; // Time spent in hours
}

export interface TaskStatus {
  id: string;
  name: string;
  color: string;
  order: number;
  isDefault: boolean;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  statusId?: string;
  priority?: string;
  dueDate?: string;
  startTime?: string;
  endTime?: string;
  estimatedHours?: number;
  createdAt: string;
  // Pola przypomnienia
  reminderEnabled?: boolean;
  reminderTime?: string;
  reminderType?: string;
  reminderValue?: number;
  project: {
    id: string;
    name: string;
    color?: string;
    archived?: boolean;
    team: {
      id: string;
      name: string;
    };
  };
  assignee?: User;
  createdBy?: User;
  taskStatus?: TaskStatus;
  subtasks: {
    id: string;
    title: string;
    isCompleted: boolean;
  }[];
  comments: {
    id: string;
    content: string;
    createdAt: string;
    author: {
      id: string;
      name: string;
      avatarUrl?: string;
    };
  }[];
  timeEntries?: {
    id: string;
    hours: number;
    description?: string;
    date: string;
    user: User;
  }[];
  images?: TaskImage[];
  attachments?: TaskAttachment[];
  todos?: Todo[];
}

export interface TaskUpdateData extends Partial<Task> {
  assigneeId?: string;
  statusId?: string;
  projectId?: string;
}

export interface SystemChange {
  id: string;
  title: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isRead?: boolean;
  isVisible?: boolean;
  createdBy: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
}
