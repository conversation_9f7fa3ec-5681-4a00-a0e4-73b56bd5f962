generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String            @id @default(cuid())
  name              String?
  email             String            @unique
  password          String
  avatarUrl         String?
  phone             String?
  location          String?
  bio               String?
  jobTitle          String?
  company           String?
  website           String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  role              String            @default("user")
  accounts          Account[]
  comments          Comment[]
  uploadedDocuments ProjectDocument[]
  sessions          Session[]
  assignedTasks     Task[]
  createdTasks      Task[]            @relation("TaskCreator")
  blockedTasks      Task[]            @relation("TaskBlocker")
  timeEntries       TimeEntry[]
  teams             Team[]            @relation("TeamMembers")
  systemChanges     SystemChange[]    @relation("SystemChangeCreator")
  systemChangeReads SystemChangeRead[]
  pushSubscriptions PushSubscription[]
  createdChatRooms  ChatRoom[]        @relation("ChatRoomCreator")
  sentMessages      Message[]         @relation("MessageSender")
  chatRooms         UserChatRoom[]
  taskAttachments   TaskAttachment[]
  passwordResetTokens PasswordResetToken[]
}

model Team {
  id        String    @id @default(cuid())
  name      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  projects  Project[]
  members   User[]    @relation("TeamMembers")
}

model Project {
  id            String            @id @default(cuid())
  name          String
  description   String?
  readme        String?           // README content with WYSIWYG editor
  status        String            @default("In Progress")
  imageUrl      String?
  repositoryUrl String?
  databaseUrl   String?
  serverUrl     String?
  apiUrl        String?
  adminPanelUrl String?
  stagingUrl    String?
  productionUrl String?
  credentials   String?
  shareToken    String?           @unique // Token for public sharing
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  teamId        String
  color         String            @default("#3B82F6")
  icon          String?
  archived      Boolean           @default(false)
  team          Team              @relation(fields: [teamId], references: [id])
  documents     ProjectDocument[]
  tasks         Task[]
  chatRooms     ChatRoom[]
}

model Task {
  id             String      @id @default(cuid())
  title          String
  description    String?
  statusId       String?
  priority       String?
  dueDate        DateTime?
  startTime      DateTime?   // Czas rozpoczęcia zadania
  endTime        DateTime?   // Czas zakończenia zadania
  estimatedHours Float?
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  projectId      String?
  assigneeId     String?
  createdById    String?
  isBlocked      Boolean     @default(false)
  blockReason    String?
  blockedAt      DateTime?
  unblockedAt    DateTime?
  blockedById    String?
  // Pola związane z przypomnieniami
  reminderEnabled Boolean    @default(false)
  reminderTime   DateTime?   // Kiedy wysłać przypomnienie
  reminderType   String?     // "hours" lub "days"
  reminderValue  Int?        // Ile godzin/dni przed zadaniem
  comments       Comment[]
  subtasks       Subtask[]
  assignee       User?       @relation(fields: [assigneeId], references: [id])
  createdBy      User?       @relation("TaskCreator", fields: [createdById], references: [id])
  project        Project?    @relation(fields: [projectId], references: [id])
  taskStatus     TaskStatus? @relation(fields: [statusId], references: [id])
  images         TaskImage[]
  attachments    TaskAttachment[]
  timeEntries    TimeEntry[]
  todos          Todo[]
  blockedBy      User?       @relation("TaskBlocker", fields: [blockedById], references: [id])
}

model Todo {
  id          String   @id @default(cuid())
  title       String
  isCompleted Boolean  @default(false)
  timeSpent   Float?   // Time spent in hours
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  taskId      String
  task        Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
}

model TaskStatus {
  id        String   @id @default(cuid())
  name      String   @unique
  color     String   @default("#6B7280")
  order     Int      @unique
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  tasks     Task[]
}

model Subtask {
  id          String   @id @default(cuid())
  title       String
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  taskId      String
  task        Task     @relation(fields: [taskId], references: [id])
}

model TaskImage {
  id        String   @id @default(cuid())
  filename  String
  url       String
  mimeType  String
  size      Int
  createdAt DateTime @default(now())
  taskId    String
  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
}

model TaskAttachment {
  id          String   @id @default(cuid())
  filename    String
  originalName String
  url         String
  mimeType    String
  size        Int
  description String?
  category    String?
  createdAt   DateTime @default(now())
  taskId      String
  uploadedById String
  task        Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  uploadedBy  User     @relation(fields: [uploadedById], references: [id], onDelete: Cascade)
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  taskId    String
  authorId  String
  author    User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
}

model TimeEntry {
  id          String   @id @default(cuid())
  hours       Float
  description String?
  date        DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  taskId      String
  userId      String
  task        Task     @relation(fields: [taskId], references: [id])
  user        User     @relation(fields: [userId], references: [id])
}

model ProjectDocument {
  id           String   @id @default(cuid())
  filename     String
  url          String
  mimeType     String
  size         Int
  description  String?
  category     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  projectId    String
  uploadedById String
  project      Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  uploadedBy   User     @relation(fields: [uploadedById], references: [id])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  userAgent    String?
  ipAddress    String?
  createdAt    DateTime @default(now())
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model SystemChange {
  id          String   @id @default(cuid())
  title       String
  description String?
  type        String   @default("info") // info, warning, success, error
  isVisible   Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String
  createdBy   User     @relation("SystemChangeCreator", fields: [createdById], references: [id])
  readBy      SystemChangeRead[]
}

model SystemChangeRead {
  id        String       @id @default(cuid())
  userId    String
  changeId  String
  readAt    DateTime     @default(now())
  user      User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  change    SystemChange @relation(fields: [changeId], references: [id], onDelete: Cascade)

  @@unique([userId, changeId])
}

model PushSubscription {
  id        String   @id @default(cuid())
  userId    String
  endpoint  String
  p256dh    String
  auth      String
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, endpoint])
}

model ChatRoom {
  id          String         @id @default(cuid())
  name        String?
  type        String         @default("group") // "direct", "group", or "project"
  projectId   String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  createdById String
  createdBy   User           @relation("ChatRoomCreator", fields: [createdById], references: [id])
  project     Project?       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  messages    Message[]
  members     UserChatRoom[]
}

model Message {
  id         String   @id @default(cuid())
  content    String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  chatRoomId String
  senderId   String
  chatRoom   ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  sender     User     @relation("MessageSender", fields: [senderId], references: [id])
}

model UserChatRoom {
  id         String   @id @default(cuid())
  userId     String
  chatRoomId String
  joinedAt   DateTime @default(now())
  lastReadAt DateTime?
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  chatRoom   ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)

  @@unique([userId, chatRoomId])
}

model SystemSettings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String?
  description String?
  isEncrypted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
