<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Notification Bell</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔔 Test Notification Bell - TeamFlow</h1>
    
    <div class="test-section">
        <h3>1. Test API Endpoints</h3>
        <p>Sprawdzenie czy API powiadomień działa poprawnie</p>
        <button onclick="testNotificationsAPI()">Test /api/notifications/chat</button>
        <button onclick="testSpecificRoom()">Test /api/notifications/chat/[roomId]</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Socket.IO Connection</h3>
        <p>Sprawdzenie połączenia Socket.IO</p>
        <button onclick="testSocketConnection()">Test Socket.IO</button>
        <div id="socket-results"></div>
    </div>

    <div class="test-section">
        <h3>3. Instrukcje testowania Notification Bell</h3>
        <div class="warning">
            <h4>📋 Kroki do wykonania w aplikacji:</h4>
            <ol>
                <li><strong>Otwórz aplikację</strong>: <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
                <li><strong>Zaloguj się</strong> na konto (np. <EMAIL> / admin123)</li>
                <li><strong>Sprawdź header</strong>: Czy widzisz ikonę dzwonka w prawym górnym rogu?</li>
                <li><strong>Kliknij dzwonek</strong>: Czy otwiera się popover z powiadomieniami?</li>
                <li><strong>Test real-time</strong>: Otwórz drugą kartę, zaloguj się jako inny użytkownik i wyślij wiadomość</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h3>4. Logi aplikacji</h3>
        <p>Sprawdź logi w konsoli przeglądarki i terminalu serwera</p>
        <button onclick="showConsoleInstructions()">Pokaż instrukcje debugowania</button>
        <div id="debug-instructions" style="display: none;">
            <h4>🔍 Co sprawdzić w konsoli przeglądarki:</h4>
            <ul>
                <li>Brak błędów JavaScript</li>
                <li>"Connected to Socket.IO server"</li>
                <li>Brak błędów z useNotifications hook</li>
            </ul>
            
            <h4>🔍 Co sprawdzić w logach serwera:</h4>
            <ul>
                <li>"User [id] registered with socket [socketId]"</li>
                <li>"User [id] ([name]) is now online"</li>
                <li>GET /api/notifications/chat (status 200)</li>
            </ul>
        </div>
    </div>

    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3001';
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `test-section ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            resultsDiv.appendChild(logEntry);
            console.log(`[${timestamp}] ${message}`);
        }

        async function testNotificationsAPI() {
            log('🧪 Testowanie /api/notifications/chat...');
            
            try {
                const response = await fetch(`${API_BASE}/api/notifications/chat`, {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ API działa! Status: ${response.status}`, 'success');
                    log(`📊 Dane: ${JSON.stringify(data, null, 2)}`);
                    document.getElementById('api-results').innerHTML = `
                        <div class="status">✅ API Response (${response.status}):</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    log(`❌ API Error: ${response.status} - ${data.error}`, 'error');
                    document.getElementById('api-results').innerHTML = `
                        <div class="status">❌ API Error (${response.status}):</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                log(`❌ Network Error: ${error.message}`, 'error');
                document.getElementById('api-results').innerHTML = `
                    <div class="status">❌ Network Error:</div>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testSpecificRoom() {
            log('🧪 Testowanie /api/notifications/chat/[roomId]...');
            
            // Użyj przykładowego ID z seed.ts
            const testRoomId = 'chat-general';
            
            try {
                const response = await fetch(`${API_BASE}/api/notifications/chat/${testRoomId}`, {
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    log(`✅ Room API działa! Status: ${response.status}`, 'success');
                    log(`📊 Dane pokoju: ${JSON.stringify(data, null, 2)}`);
                } else {
                    log(`⚠️ Room API Response: ${response.status} - ${data.error || JSON.stringify(data)}`, 'warning');
                }
                
                document.getElementById('api-results').innerHTML += `
                    <div class="status">Room API Response (${response.status}):</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                log(`❌ Room API Error: ${error.message}`, 'error');
            }
        }

        async function testSocketConnection() {
            log('🧪 Testowanie Socket.IO connection...');
            
            if (typeof io === 'undefined') {
                log('❌ Socket.IO library nie jest załadowana', 'error');
                document.getElementById('socket-results').innerHTML = `
                    <div class="status">❌ Socket.IO nie jest dostępne</div>
                    <p>Socket.IO jest dostępne tylko w głównej aplikacji TeamFlow</p>
                `;
                return;
            }
            
            try {
                const socket = io(API_BASE);
                
                socket.on('connect', () => {
                    log('✅ Socket.IO połączony!', 'success');
                    document.getElementById('socket-results').innerHTML = `
                        <div class="status">✅ Socket.IO Status: Połączony</div>
                        <p>Socket ID: ${socket.id}</p>
                    `;
                });
                
                socket.on('disconnect', () => {
                    log('⚠️ Socket.IO rozłączony', 'warning');
                });
                
                socket.on('connect_error', (error) => {
                    log(`❌ Socket.IO Error: ${error.message}`, 'error');
                    document.getElementById('socket-results').innerHTML = `
                        <div class="status">❌ Socket.IO Error:</div>
                        <pre>${error.message}</pre>
                    `;
                });
                
                // Test connection timeout
                setTimeout(() => {
                    if (!socket.connected) {
                        log('⚠️ Socket.IO nie połączył się w ciągu 5 sekund', 'warning');
                        document.getElementById('socket-results').innerHTML = `
                            <div class="status">⚠️ Socket.IO Timeout</div>
                            <p>Sprawdź czy serwer działa na ${API_BASE}</p>
                        `;
                    }
                }, 5000);
                
            } catch (error) {
                log(`❌ Socket.IO Test Error: ${error.message}`, 'error');
            }
        }

        function showConsoleInstructions() {
            const instructionsDiv = document.getElementById('debug-instructions');
            instructionsDiv.style.display = instructionsDiv.style.display === 'none' ? 'block' : 'none';
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            log('🚀 Rozpoczynanie testów Notification Bell...');
            log('📱 Otwórz DevTools (F12) aby zobaczyć szczegółowe logi');
            
            // Test if we can reach the server
            fetch(`${API_BASE}/api/auth/session`, { credentials: 'include' })
                .then(response => {
                    if (response.ok) {
                        log('✅ Serwer jest dostępny', 'success');
                    } else {
                        log('⚠️ Serwer odpowiada, ale może wymagać logowania', 'warning');
                    }
                })
                .catch(error => {
                    log('❌ Nie można połączyć się z serwerem', 'error');
                });
        });
    </script>
</body>
</html>
