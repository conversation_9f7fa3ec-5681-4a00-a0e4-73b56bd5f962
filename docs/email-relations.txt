=== SYSTEM WYSYŁANIA MAILI - DOKUMENTACJA ===

Data utworzenia: 2025-08-03
Autor: Augment Agent

=== PRZEGLĄD FUNKCJONALNOŚCI ===

System wysyłania maili w TeamFlow umożliwia:
1. Wysyłanie maili powitalnych po rejestracji
2. Resetowanie hasła przez email
3. Konfigurację SMTP przez panel administratora
4. Testowanie połączenia SMTP

=== STRUKTURA BAZY DANYCH ===

1. SYSTEMSETTINGS
   - Tabela: SystemSettings
   - Pola: id, key, value, description, isEncrypted, createdAt, updatedAt
   - Przechowuje konfigurację SMTP (host, port, user, password, from_email, from_name)
   - Hasło SMTP jest oznaczone jako zaszyfrowane (isEncrypted: true)

2. PASSWORDRESETTOKEN
   - Tabela: PasswordResetToken
   - Pola: id, token, userId, expires, used, createdAt
   - Relacja: belongsTo User
   - Przechowuje tokeny do resetowania hasła (ważne 1 godzinę)

3. USER (rozszerzony)
   - Dodana relacja: hasMany PasswordResetToken

=== KONFIGURACJA SMTP ===

Ustawienia SMTP przechowywane w zmiennych środowiskowych (.env):
- SMTP_HOST: Adres serwera SMTP (np. smtp.gmail.com)
- SMTP_PORT: Port serwera SMTP (np. 587)
- SMTP_SECURE: Czy używać SSL/TLS (true/false)
- SMTP_USER: Nazwa użytkownika SMTP (email)
- SMTP_PASSWORD: Hasło SMTP
- SMTP_FROM_EMAIL: Adres email nadawcy
- SMTP_FROM_NAME: Nazwa nadawcy (domyślnie "Nexus")

=== BIBLIOTEKA EMAIL (src/lib/email.ts) ===

1. FUNKCJE GŁÓWNE:
   - getSMTPSettings(): Pobiera ustawienia SMTP ze zmiennych środowiskowych
   - createTransporter(): Tworzy transporter nodemailer
   - sendWelcomeEmail(): Wysyła mail powitalny po rejestracji
   - sendPasswordResetEmail(): Wysyła mail z linkiem resetującym hasło
   - testSMTPConnection(): Testuje połączenie SMTP

2. SZABLONY MAILI:
   - Mail powitalny: Responsywny HTML z informacjami o platformie
   - Mail resetowania hasła: Bezpieczny link z tokenem, ważny 1 godzinę

=== API ENDPOINTS ===

1. USTAWIENIA SMTP (ADMIN ONLY):
   - GET /api/admin/smtp-settings - Pobiera ustawienia SMTP z .env (tylko do odczytu)
   - POST /api/admin/smtp-settings/test - Testuje połączenie SMTP

2. RESETOWANIE HASŁA:
   - POST /api/auth/forgot-password - Wysyła link resetujący
   - POST /api/auth/validate-reset-token - Waliduje token resetowania
   - POST /api/auth/reset-password - Resetuje hasło

3. REJESTRACJA (ZMODYFIKOWANA):
   - POST /api/auth/register - Tworzy konto + wysyła mail powitalny

=== STRONY UŻYTKOWNIKA ===

1. /auth/forgot-password
   - Formularz do wprowadzenia emaila
   - Wysyła żądanie resetowania hasła
   - Informuje o wysłaniu maila

2. /auth/reset-password?token=XXX
   - Waliduje token przy ładowaniu
   - Formularz do ustawienia nowego hasła
   - Obsługuje nieprawidłowe/wygasłe tokeny

3. /dashboard/settings (zakładka SMTP - admin only)
   - Podgląd obecnych ustawień SMTP z .env (tylko do odczytu)
   - Test połączenia SMTP
   - Instrukcje konfiguracji .env z przykładami

=== PRZEPŁYW RESETOWANIA HASŁA ===

1. Użytkownik klika "Zapomniałeś hasła?" na stronie logowania
2. Przechodzi na /auth/forgot-password
3. Wprowadza email i wysyła formularz
4. API sprawdza czy użytkownik istnieje
5. Generuje token resetowania (32 bajty hex)
6. Zapisuje token w bazie z wygaśnięciem za 1h
7. Wysyła mail z linkiem zawierającym token
8. Użytkownik klika link w mailu
9. Przechodzi na /auth/reset-password?token=XXX
10. Strona waliduje token przy ładowaniu
11. Jeśli token ważny, pokazuje formularz nowego hasła
12. Po wysłaniu formularza API:
    - Waliduje token ponownie
    - Sprawdza czy nowe hasło różni się od obecnego
    - Hashuje nowe hasło
    - Aktualizuje hasło użytkownika
    - Oznacza token jako użyty
    - Usuwa wszystkie sesje użytkownika
13. Przekierowuje na stronę logowania

=== PRZEPŁYW REJESTRACJI Z MAILEM POWITALNYM ===

1. Użytkownik wypełnia formularz rejestracji
2. API tworzy konto w bazie danych
3. API próbuje wysłać mail powitalny (nie blokuje rejestracji)
4. Przekierowuje na stronę logowania z komunikatem sukcesu
5. Użytkownik otrzymuje mail powitalny z informacjami o platformie

=== BEZPIECZEŃSTWO ===

1. TOKENY RESETOWANIA:
   - Generowane kryptograficznie (crypto.randomBytes)
   - Ważne tylko 1 godzinę
   - Jednorazowego użytku
   - Usuwane po użyciu lub wygaśnięciu

2. WALIDACJA:
   - Sprawdzanie formatu email
   - Minimalna długość hasła (8 znaków)
   - Nowe hasło musi różnić się od obecnego
   - Wszystkie sesje usuwane po zmianie hasła

3. USTAWIENIA SMTP:
   - Dostępne tylko dla administratorów
   - Hasło SMTP oznaczone jako zaszyfrowane
   - Walidacja wszystkich wymaganych pól

=== OBSŁUGA BŁĘDÓW ===

1. SMTP NIE SKONFIGUROWANE:
   - Funkcje email zwracają błąd
   - Rejestracja działa, ale bez maila powitalnego
   - Admin może skonfigurować SMTP w ustawieniach

2. BŁĘDY WYSYŁANIA:
   - Logowane w konsoli
   - Nie blokują głównych funkcjonalności
   - Użytkownik informowany o problemie

3. NIEPRAWIDŁOWE TOKENY:
   - Jasne komunikaty o błędach
   - Możliwość wygenerowania nowego tokenu
   - Automatyczne przekierowania

=== POPULARNE USTAWIENIA SMTP ===

Gmail:
- Host: smtp.gmail.com
- Port: 587
- TLS: true
- Uwaga: Wymaga hasła aplikacji, nie hasła konta

Outlook:
- Host: smtp-mail.outlook.com
- Port: 587
- TLS: true

SendGrid:
- Host: smtp.sendgrid.net
- Port: 587
- TLS: true
- Uwaga: Wymaga API key jako hasła

=== PLIKI ZWIĄZANE Z SYSTEMEM MAILI ===

BIBLIOTEKI:
- src/lib/email.ts - Główna biblioteka do wysyłania maili

API:
- src/app/api/admin/smtp-settings/route.ts - Zarządzanie ustawieniami SMTP
- src/app/api/admin/smtp-settings/test/route.ts - Test połączenia SMTP
- src/app/api/auth/forgot-password/route.ts - Wysyłanie linku resetującego
- src/app/api/auth/validate-reset-token/route.ts - Walidacja tokenu
- src/app/api/auth/reset-password/route.ts - Resetowanie hasła
- src/app/api/auth/register/route.ts - Rejestracja z mailem powitalnym

STRONY:
- src/app/auth/forgot-password/page.tsx - Strona "Nie pamiętam hasła"
- src/app/auth/reset-password/page.tsx - Strona resetowania hasła

KOMPONENTY:
- src/components/settings/smtp-settings.tsx - Panel konfiguracji SMTP
- src/components/settings/settings-content.tsx - Zakładka SMTP w ustawieniach
- src/components/auth/login-form.tsx - Link "Zapomniałeś hasła?"

BAZA DANYCH:
- prisma/schema.prisma - Modele SystemSettings i PasswordResetToken

=== ZALEŻNOŚCI ===

- nodemailer: Wysyłanie maili przez SMTP
- @types/nodemailer: Typy TypeScript dla nodemailer
- crypto: Generowanie bezpiecznych tokenów (wbudowane w Node.js)
- bcryptjs: Hashowanie haseł (już istniejące)

=== TESTOWANIE ===

1. KONFIGURACJA SMTP:
   - Przejdź do /dashboard/settings → zakładka SMTP
   - Wprowadź dane SMTP
   - Kliknij "Testuj połączenie"

2. MAIL POWITALNY:
   - Zarejestruj nowe konto
   - Sprawdź czy mail powitalny został wysłany

3. RESETOWANIE HASŁA:
   - Przejdź na /auth/forgot-password
   - Wprowadź email istniejącego użytkownika
   - Sprawdź mail z linkiem resetującym
   - Kliknij link i ustaw nowe hasło
   - Zaloguj się nowym hasłem

=== AKTUALIZACJA (2025-08-03) ===

Dodano kompletny system wysyłania maili z:
- Konfiguracją SMTP przez panel administratora
- Mailem powitalnym po rejestracji z informacjami o systemie
- Resetowaniem hasła przez email
- Bezpieczną obsługą tokenów
- Responsywnymi szablonami maili z nowoczesnym designem
- Testowaniem połączenia SMTP
- Pełną walidacją i obsługą błędów
- Dostosowaniem layoutu stron resetowania hasła do stylu logowania/rejestracji

=== SZABLON MAILA POWITALNEGO ===

Nowy szablon maila powitalnego zawiera:
- Nowoczesny design z gradientami i glassmorphism
- Informacje o platformie Nexus z landing page
- Statystyki: 10,000+ użytkowników, 50,000+ projektów, 98% zadowolenie
- Szczegółowe opisy głównych funkcji:
  * Zarządzanie zadaniami z tablicami kanban
  * Współpraca zespołowa w czasie rzeczywistym
  * Śledzenie czasu i analiza produktywności
  * Widok kalendarza i planowanie
  * Analityka i szczegółowe raporty
- Korzyści platformy (intuicyjność, skalowalność, bezpieczeństwo, cena)
- Atrakcyjny przycisk CTA do logowania
- Responsywny design działający w różnych klientach email

=== MIGRACJA USTAWIEŃ SMTP DO .ENV (2025-08-03) ===

Ustawienia SMTP zostały przeniesione z bazy danych do zmiennych środowiskowych:

POWODY MIGRACJI:
- Lepsze bezpieczeństwo (hasła nie w bazie danych)
- Łatwiejsze zarządzanie środowiskami (dev/staging/prod)
- Zgodność z best practices dla aplikacji 12-factor
- Prostsze deployment i konfiguracja

ZMIANY W KODZIE:
- src/lib/email.ts: getSMTPSettings() czyta z process.env
- src/app/api/admin/smtp-settings/route.ts: tylko GET (odczyt z .env)
- src/components/settings/smtp-settings.tsx: interfejs tylko do odczytu
- .env: dodane zmienne SMTP_*

KONFIGURACJA:
Dodaj do pliku .env:
```
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM_EMAIL="<EMAIL>"
SMTP_FROM_NAME="Nexus"
```

INTERFEJS ADMINISTRATORA:
- Pokazuje obecne ustawienia z .env (tylko do odczytu)
- Umożliwia testowanie połączenia SMTP
- Zawiera instrukcje konfiguracji z przykładami
- Kod przykładowy .env z kolorowym podświetleniem
