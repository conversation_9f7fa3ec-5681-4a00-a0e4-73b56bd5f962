{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npx prisma migrate dev:*)", "Bash(npx prisma migrate diff:*)", "Bash(npx prisma migrate reset:*)", "Bash(npx prisma generate:*)", "Bash(npx tsx:*)", "Bash(npx prisma:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npx tsc:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "Bash(timeout 30 npm run build -- --no-lint)", "Bash(node:*)", "<PERSON><PERSON>(timeout 15 npm run dev)", "<PERSON><PERSON>(cat:*)", "Bash(timeout 30 npm run build)", "Bash(grep:*)", "WebFetch(domain:trello.com)"], "deny": []}}